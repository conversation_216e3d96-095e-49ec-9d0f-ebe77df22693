<?php

use Illuminate\Support\Facades\Route;

Route::prefix('authentication')->controller(App\Http\Controllers\AuthenticationController::class)->group(function () {
    Route::post('/login', 'login')->name('authentication.login');
    Route::post('/login/google', 'googleLogin')->name('authentication.googleLogin');
    Route::post('/login/apple', 'appleLogin')->name('authentication.appleLogin');
    Route::post('/register', 'register')->name('authentication.register');
    Route::post('/logout', 'logout')->name('authentication.logout');
    Route::post('/verify-email/{id}/{hash}', 'verifyEmail')->name('authentication.verifyEmail');
    Route::post('/forgot-password', 'sendResetPasswordEmail')->name('authentication.sendResetPasswordEmail');
    Route::post('/reset-password', 'resetPassword')->name('authentication.resetPassword');
});

Route::prefix('settings')->controller(App\Http\Controllers\SettingsController::class)->group(function () {
    Route::get('/', 'index')->name('settings.index');
    Route::post('/', 'update')->name('settings.update');
    Route::post('/password', 'updatePassword')->name('settings.updatePassword');
    Route::post('/profile-picture', 'updateProfilePicture')->name('settings.updateProfilePicture');
    Route::post('/language', 'updateLanguage')->name('settings.updateLanguage');
    Route::post('/tutorial-done', 'tutorialDone')->name('settings.tutorialDone');
});

Route::prefix('books')->controller(App\Http\Controllers\BooksController::class)->group(function () {
    Route::get('/', 'index')->name('books.index');
    Route::get('/{book:uuid}', 'show')->name('books.show');
    Route::get('/{book:uuid}/similar', 'similar')->name('books.similar');
    Route::post('/{book:google_id}/not-free', 'setNotFree')->name('books.setNotFree');
    Route::post('/{book:uuid}/list/{bookList}/add', 'addToList')->name('books.addToList');
    Route::post('/{book:uuid}/list/{bookList}/remove', 'removeFromList')->name('books.removeFromList');
    Route::post('/{book:uuid}/share', 'share')->name('books.share');
    Route::post('/{book:uuid}/summary', 'summary')->name('books.summary');
    Route::get('/{book:uuid}/summary-tts/{languageCode}', 'summaryTTS')->name('books.summaryTTS');
});

Route::prefix('authors')->controller(App\Http\Controllers\AuthorsController::class)->group(function () {
    Route::get('/', 'index')->name('authors.index');
    Route::get('/audiobooks', 'indexAudiobooks')->name('authors.indexAudiobooks');
    Route::get('/followed', 'followed')->name('authors.followed');
    Route::get('/{author:uuid}', 'show')->name('authors.show');
    Route::post('/{author:uuid}/follow', 'follow')->name('authors.follow');
    Route::post('/{author:uuid}/unfollow', 'unfollow')->name('authors.unfollow');
});

Route::prefix('likes')->controller(App\Http\Controllers\LikesController::class)->group(function () {
    Route::get('/', 'index')->name('likes.index');
    Route::post('/like/{book:uuid}', 'like')->name('likes.like');
    Route::post('/dislike/{book:uuid}', 'dislike')->name('likes.dislike');
    Route::post('/remove/{book:uuid}', 'removeLike')->name('likes.removeLike');
});

Route::prefix('book-lists')->controller(App\Http\Controllers\BookListsController::class)->group(function () {
    Route::get('/', 'index')->name('bookLists.index');
    Route::post('/', 'store')->name('bookLists.store');
    Route::get('/{bookList}', 'show')->name('bookLists.show');
    Route::post('/{bookList}', 'update')->name('bookLists.update');
    Route::delete('/{bookList}', 'destroy')->name('bookLists.destroy');
});

Route::prefix('book-read-statuses')->controller(App\Http\Controllers\BookReadStatusesController::class)->group(function () {
    Route::get('/', 'index')->name('bookReadStatuses.index');
    Route::get('/{type}', 'show')->name('bookReadStatuses.show');
    Route::post('/add/{book:uuid}', 'setBookStatus')->name('bookReadStatuses.setBookStatus');
    Route::post('/remove/{book:uuid}', 'removeBookStatus')->name('bookReadStatuses.removeBookStatus');
});

Route::prefix('recommendations')->controller(App\Http\Controllers\RecommendationsController::class)->group(function () {
    Route::get('/', 'index')->name('recommendations.index');
});

Route::prefix('languages')->controller(App\Http\Controllers\LanguagesController::class)->group(function () {
    Route::get('/', 'index')->name('languages.index');
    Route::get('/audiobooks', 'indexAudiobooks')->name('languages.indexAudiobooks');
});

Route::prefix('subjects')->controller(App\Http\Controllers\SubjectsController::class)->group(function () {
    Route::get('/', 'index')->name('subjects.index');
    Route::get('/audiobooks', 'indexAudiobooks')->name('subjects.indexAudiobooks');
});

Route::prefix('audiobooks')->controller(App\Http\Controllers\AudiobooksController::class)->group(function () {
    Route::get('/', 'index')->name('audiobooks.index');
    Route::get('/{audiobook:uuid}', 'show')->name('audiobooks.show');
});

Route::prefix('achievements')->controller(App\Http\Controllers\AchievementsController::class)->group(function () {
    Route::get('/', 'index')->name('achievements.index');
});

Route::prefix('ai-conversations')->controller(App\Http\Controllers\AiConversationsController::class)->group(function () {
    Route::get('/', 'index')->name('aiConversations.index');
    Route::post('/', 'store')->name('aiConversations.store');
    Route::delete('/{aiConversation}', 'destroy')->name('aiConversations.destroy');
});

Route::prefix('reading-activities')->controller(App\Http\Controllers\ReadingActivitiesController::class)->group(function () {
    Route::get('/{book:uuid}', 'index')->name('readingActivities.index');
    Route::post('/', 'store')->name('readingActivities.store');
    Route::post('/{activityUuid}', 'update')->name('readingActivities.update');
    Route::delete('/{activityUuid}', 'destroy')->name('readingActivities.destroy');
});
