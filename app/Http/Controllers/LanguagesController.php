<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\LanguageResource;
use App\Models\Language;
use Illuminate\Support\Facades\Cache;

final class LanguagesController extends Controller
{
    public function index(): array
    {
        if (Cache::has('languages')) {
            return Cache::get('languages');
        }

        $languages = Language::where('show_in_menu', true)
            ->whereNotNull('code')
            ->orderBy('name')
            ->get();
        $languages = LanguageResource::collection($languages)->jsonSerialize();

        Cache::forever('languages', $languages);

        return $languages;
    }

    public function indexAudiobooks(): array
    {
        if (Cache::has('languages-audiobooks')) {
            return Cache::get('languages-audiobooks');
        }

        $languages = Language::where('show_in_menu', true)
            ->has('audiobooks')
            ->orderBy('name')
            ->get();
        $languages = LanguageResource::collection($languages)->jsonSerialize();

        Cache::forever('languages-audiobooks', $languages);

        return $languages;
    }
}
