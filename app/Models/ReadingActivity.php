<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\ReadingActivity
 *
 * @property int $id
 * @property string $uuid
 * @property Carbon|null $start_date
 * @property Carbon|null $end_date
 * @property int|null $pages_read
 * @property int|null $total_pages
 * @property int|null $language_id
 * @property int $book_id
 * @property int $user_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class ReadingActivity extends Model
{
    protected $table = 'reading_activities';
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }
}
