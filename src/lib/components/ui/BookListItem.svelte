<script lang="ts">
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import GiftSvg from '$lib/components/svg/GiftSvg.svelte';
    import LanguageSvg from '$lib/components/svg/LanguageSvg.svelte';
    import PencilSquareSvg from '$lib/components/svg/PencilSquareSvg.svelte';
    import TrashSvg from '$lib/components/svg/TrashSvg.svelte';
    import type Book from '$lib/domain/Book';
    import type ReadingActivity from '$lib/domain/ReadingActivity';
    import type SearchBook from '$lib/domain/SearchBook';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book | SearchBook;
    export let deleteCallback: ((bookIdentifier: string) => void) | undefined = undefined;
    export let onEditActivity: ((activity: ReadingActivity | null) => void) | undefined = undefined;
    export let showProgressForAll = false;

    $: isBook = 'readingActivity' in book;
    $: latestActivity = isBook && book.readingActivity && book.readingActivity.length > 0
        ? book.readingActivity[0]
        : null;
    $: hasProgress = latestActivity && latestActivity.pagesRead !== null && latestActivity.totalPages !== null && latestActivity.totalPages > 0;
    $: progressPercentage = hasProgress ? (latestActivity.pagesRead / latestActivity.totalPages) * 100 : 0;

    function handleEditClick(event: Event) {
        event.preventDefault();
        event.stopPropagation();

        if (onEditActivity) {
            onEditActivity(latestActivity);
        }
    }
</script>

<a href={AppRoutes.book(book.uuid)} class="group block">
    <div
        class="relative flex h-full flex-col rounded-lg border border-gray-200 bg-gray-50 p-3 shadow transition-all hover:bg-gray-100 hover:shadow-md dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600"
    >
        {#if deleteCallback !== undefined}
            <button
                type="button"
                class="absolute right-2 top-2 z-10 rounded-full p-1 text-gray-400 transition-all hover:bg-red-100 hover:text-red-600 dark:text-gray-500 dark:hover:bg-red-900/20 dark:hover:text-red-400"
                on:click={(event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    deleteCallback(book.uuid);
                }}
            >
                <TrashSvg/>
            </button>
        {/if}
        <div class="mb-3 flex justify-center">
            {#if book.cover}
                <img src={book.cover} class="h-32 rounded-lg object-cover shadow-sm" alt="Book cover"/>
            {:else}
                <div class="flex h-32 items-center justify-center rounded-lg bg-gray-200 shadow-sm dark:bg-gray-600">
                    <BookOpenSvg svgClass="h-8 w-8 text-gray-400 dark:text-gray-500"/>
                </div>
            {/if}
        </div>
        <div class="flex flex-1 flex-col">
            <div class="text-center flex-1">
                <div class="h-10 flex items-start justify-center">
                    <h3 class="line-clamp-2 text-sm font-semibold leading-tight text-gray-700 dark:text-white">
                        {book.title}
                    </h3>
                </div>
                <p class="mt-1 line-clamp-1 text-sm text-gray-500 dark:text-gray-400">
                    {book.authors.join(', ')}
                </p>
            </div>
            <div style:min-height="60px" class="mt-auto">
                <div style:min-height="32px" class="space-y-1">
                    <div class="flex items-center justify-center text-sm text-gray-500 dark:text-gray-400">
                        <LanguageSvg svgClass="mr-1 h-3 w-3"/>
                        <span class="line-clamp-1">{book.languages.map((language) => language.text).join(', ')}</span>
                    </div>
                    <div
                        style:min-height="16px"
                        class="flex items-center justify-center text-sm text-gray-500 dark:text-gray-400"
                    >
                        {#if book.isFree}
                            <GiftSvg svgClass="mr-1 h-3 w-3"/>
                            <span>{$t('book.read')}</span>
                        {/if}
                    </div>
                </div>
                {#if showProgressForAll}
                    <div class="mt-3">
                        <div class="text-xs text-center text-gray-500 dark:text-gray-400 font-medium">
                            {Math.round(progressPercentage)}%
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="w-full bg-gray-300 rounded-full h-2 dark:bg-gray-500">
                                    <div
                                        style:width="{progressPercentage}%"
                                        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                    ></div>
                                </div>
                            </div>
                            {#if onEditActivity}
                                <button
                                    type="button"
                                    class="ml-2 text-gray-400 hover:text-blue-600 transition-colors"
                                    title={$t('lists.editReadingProgress')}
                                    on:click={handleEditClick}
                                >
                                    <PencilSquareSvg svgClass="h-4 w-4"/>
                                </button>
                            {/if}
                        </div>
                        <div class="text-xs text-center text-gray-500 dark:text-gray-400">
                            {#if hasProgress}
                                {latestActivity.pagesRead || 0} / {latestActivity.totalPages || 0} pages
                            {:else}
                                &nbsp;
                            {/if}
                        </div>
                    </div>
                {/if}
            </div>
        </div>
    </div>
</a>
