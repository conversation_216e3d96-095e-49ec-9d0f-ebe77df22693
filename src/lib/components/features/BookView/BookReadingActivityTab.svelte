<script lang="ts">
    import {onMount} from 'svelte';
    import ReadingActivitiesApiClient from '$lib/api/ReadingActivitiesApiClient';
    import ReadingActivityModal from '$lib/components/features/ReadingActivityModal.svelte';
    import SpinnerSvg from '$lib/components/svg/SpinnerSvg.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import Modal from '$lib/components/ui/Modal.svelte';
    import type Book from '$lib/domain/Book';
    import type Language from '$lib/domain/Language';
    import type ReadingActivity from '$lib/domain/ReadingActivity';
    import {t} from '$lib/localization/Localization';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';

    export let book: Book;

    let activities: ReadingActivity[] = [];
    let loading = true;
    let showFormModal = false;
    let showDeleteModal = false;
    let editingActivity: ReadingActivity | null = null;
    let deletingActivity: ReadingActivity | null = null;
    let isEditing = false;
    let modalTriggeredByStatusChange = false;

    onMount(() => {
        loadActivities();
    });



    async function loadActivities() {
        loading = true;
        const apiClient = new ReadingActivitiesApiClient();
        const response = await apiClient.index(book.uuid);

        loading = false;

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: $t('notifications.genericError'),
                type: 'warning'
            });

            return;
        }

        activities = response.data;
    }

    function openAddModal() {
        isEditing = false;
        editingActivity = null;
        modalTriggeredByStatusChange = false;
        showFormModal = true;
    }

    export function openAddModalFromExternal(triggeredByStatusChange: boolean = false) {
        isEditing = false;
        editingActivity = null;
        modalTriggeredByStatusChange = triggeredByStatusChange;
        showFormModal = true;
    }

    function openEditModal(activity: ReadingActivity) {
        isEditing = true;
        editingActivity = activity;
        modalTriggeredByStatusChange = false;
        showFormModal = true;
    }

    function openDeleteModal(activity: ReadingActivity) {
        deletingActivity = activity;
        showDeleteModal = true;
    }

    async function onActivitySaved() {
        showFormModal = false;
        editingActivity = null;
        isEditing = false;
        modalTriggeredByStatusChange = false;
        await loadActivities();
    }

    async function deleteActivity() {
        if (!deletingActivity) {
            return;
        }

        const apiClient = new ReadingActivitiesApiClient();
        const response = await apiClient.destroy(deletingActivity.id);

        if (response.ok) {
            showDeleteModal = false;
            deletingActivity = null;
            await loadActivities();
        } else {
            ToastNotificationsStore.push({
                text: $t('notifications.genericError'),
                type: 'warning'
            });
        }
    }

    function formatDate(dateString: string | null): string {
        if (!dateString) {
            return '';
        }
        return new Date(dateString).toLocaleDateString('en-GB');
    }

    function getLanguageName(language: Language | null): string {
        return language?.text || '-';
    }
</script>

<div class="space-y-6">
    <div class="flex justify-between items-center">
        <Button title={$t('readingActivity.addActivity')} callback={openAddModal}/>
    </div>
    {#if loading}
        <div class="flex justify-center py-8">
            <SpinnerSvg svgClass="w-8 h-8"/>
        </div>
    {:else if activities.length === 0}
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
            {$t('readingActivity.noActivities')}
        </div>
    {:else}
        <div class="space-y-4">
            {#each activities as activity (activity.id)}
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div class="space-y-2">
                            <div class="flex items-center space-x-4">
                                <span class="font-medium">{formatDate(activity.startDate)}
                                    - {formatDate(activity.endDate)}</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">{getLanguageName(activity.language)}</span>
                            </div>
                            <div class="text-sm">
                                {$t('readingActivity.pages')}: {activity.pagesRead || 0} / {activity.totalPages || 0}
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <Button
                                title={$t('readingActivity.edit')}
                                callback={() => openEditModal(activity)}
                                primary={true}
                                extraClass="text-xs px-2 py-1 text-white"
                            />
                            <Button
                                title={$t('readingActivity.delete')}
                                callback={() => openDeleteModal(activity)}
                                primary={true}
                                extraClass="text-xs px-2 py-1 bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700"
                            />
                        </div>
                    </div>
                </div>
            {/each}
        </div>
    {/if}
</div>
<ReadingActivityModal
    bind:open={showFormModal}
    book={book}
    editingActivity={editingActivity}
    isEditing={isEditing}
    modalTriggeredByStatusChange={modalTriggeredByStatusChange}
    onActivitySaved={onActivitySaved}
/>
<Modal bind:open={showDeleteModal}>
    <div class="mb-4 flex justify-between rounded-t sm:mb-5">
        <div class="text-lg text-gray-900 dark:text-white md:text-xl">
            <h3 class="font-semibold">{$t('readingActivity.confirmDelete')}</h3>
        </div>
    </div>
    <div class="space-y-4">
        <p class="text-gray-700 dark:text-gray-300">
            {$t('readingActivity.confirmDeleteMessage')}
        </p>
        <div class="flex justify-end space-x-2 pt-4">
            <Button
                title="Cancel"
                callback={() => { showDeleteModal = false; deletingActivity = null; }}
                primary={false}
            />
            <Button
                title={$t('readingActivity.delete')}
                callback={deleteActivity}
                extraClass="bg-red-600 dark:bg-red-600 text-white dark:text-white hover:bg-red-700 dark:hover:bg-red-700"
            />
        </div>
    </div>
</Modal>
